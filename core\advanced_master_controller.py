"""
Advanced Master Controller for the AI-Powered Coding Assistant.
Integrates all systems and provides comprehensive capabilities with iterative analysis.
"""

import asyncio
import threading
import time
import logging
from typing import Dict, List, Optional, Any, Callable, Union, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import json
import uuid
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed

from .self_analyzing_intelligence import Self<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gence, AnalysisType, DecisionType
from .ai_code_assistant import <PERSON><PERSON>ode<PERSON>sistant, AssistantRe<PERSON>, AssistantResponse
from .rag_enhanced_system import RAGEnhancedSystem
from .context_aware_completion import ContextAwareCompletion
from .intelligent_refactoring import IntelligentRefactoring
from .predictive_debugger import PredictiveDebugger
from .performance_analyzer import PerformanceAnalyzer
from .optimization_engine import OptimizationEngine
from .multi_language_processor import MultiLanguageProcessor
from .semantic_indexer import SemanticIndexer
from .learning_system import LearningSystem

logger = logging.getLogger(__name__)

class TaskType(Enum):
    """Types of tasks the master controller can handle."""
    CODE_ANALYSIS = "code_analysis"
    CODE_GENERATION = "code_generation"
    DEBUGGING = "debugging"
    OPTIMIZATION = "optimization"
    REFACTORING = "refactoring"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    SEARCH = "search"
    EXECUTION = "execution"
    LEARNING = "learning"
    ITERATIVE_IMPROVEMENT = "iterative_improvement"
    CONVERSATION = "conversation"

class Priority(Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Task:
    """Represents a task to be executed."""
    task_id: str
    task_type: TaskType
    priority: Priority
    description: str
    input_data: Dict[str, Any]
    context: Dict[str, Any]
    created_at: float
    deadline: Optional[float] = None
    dependencies: List[str] = None
    callback: Optional[Callable] = None

@dataclass
class TaskResult:
    """Result of a completed task."""
    task_id: str
    success: bool
    result: Any
    execution_time: float
    confidence_score: float
    recommendations: List[str]
    next_steps: List[str]
    metadata: Dict[str, Any]
    iterations_performed: int = 0
    improvement_achieved: float = 0.0

@dataclass
class IterativeAnalysisResult:
    """Result of iterative analysis process."""
    initial_result: Any
    final_result: Any
    iterations: List[Dict[str, Any]]
    improvement_metrics: Dict[str, float]
    confidence_progression: List[float]
    total_time: float
    success: bool

class AdvancedMasterController:
    """Advanced master controller with iterative analysis and self-improvement capabilities."""

    def __init__(self, model_manager, workspace_dir: Path):
        """Initialize the advanced master controller.
        
        Args:
            model_manager: The model manager for AI operations
            workspace_dir: The workspace directory
        """
        self.model_manager = model_manager
        self.workspace_dir = workspace_dir
        self.lock = threading.RLock()
        
        # Initialize all subsystems
        self._initialize_subsystems()
        
        # Task management
        self.task_queue: List[Task] = []
        self.active_tasks: Dict[str, Task] = {}
        self.completed_tasks: Dict[str, TaskResult] = {}
        self.task_history: List[TaskResult] = []
        
        # Execution management
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.is_running = False
        self.background_thread: Optional[threading.Thread] = None
        
        # Iterative analysis configuration
        self.max_iterations = 5
        self.improvement_threshold = 0.1
        self.confidence_threshold = 0.8
        
        # Performance tracking
        self.metrics = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "average_execution_time": 0.0,
            "average_confidence": 0.0,
            "system_load": 0.0,
            "average_iterations": 0.0,
            "improvement_rate": 0.0
        }
        
        # Callback system
        self.task_callbacks: List[Callable[[TaskResult], None]] = []
        self.progress_callbacks: List[Callable[[str, float], None]] = []
        self.iteration_callbacks: List[Callable[[str, int, Dict[str, Any]], None]] = []

    def _initialize_subsystems(self):
        """Initialize all subsystems."""
        logger.info("Initializing advanced master controller subsystems...")
        
        # Core intelligence systems
        self.intelligence = SelfAnalyzingIntelligence(self.model_manager, self.workspace_dir)
        self.ai_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)
        self.rag_system = RAGEnhancedSystem(self.model_manager, self.workspace_dir)
        
        # Specialized processors
        self.context_completion = ContextAwareCompletion(self.model_manager, self.workspace_dir)
        self.refactoring_engine = IntelligentRefactoring(self.model_manager, self.workspace_dir)
        self.debugger = PredictiveDebugger(self.model_manager, self.workspace_dir)
        self.performance_analyzer = PerformanceAnalyzer(self.model_manager, self.workspace_dir)
        self.optimization_engine = OptimizationEngine(self.model_manager, self.workspace_dir)
        
        # Language and indexing systems
        self.language_processor = MultiLanguageProcessor(self.workspace_dir)
        self.semantic_indexer = SemanticIndexer(self.workspace_dir, self.model_manager)
        self.learning_system = LearningSystem(self.workspace_dir, self.model_manager)
        
        # Setup cross-system integration
        self._setup_integration()
        
        logger.info("All subsystems initialized successfully")

    def _setup_integration(self):
        """Setup integration between subsystems."""
        # Connect intelligence system callbacks
        self.intelligence.analysis_callbacks.append(self._on_analysis_complete)
        self.intelligence.decision_callbacks.append(self._on_decision_made)
        
        # Connect learning system
        self.learning_system.add_feedback_source(self._get_task_feedback)
        
        # Setup performance monitoring
        self.performance_analyzer.add_monitor_callback(self._on_performance_update)

    def start(self):
        """Start the master controller."""
        with self.lock:
            if self.is_running:
                logger.warning("Master controller is already running")
                return
                
            self.is_running = True
            self.background_thread = threading.Thread(target=self._background_worker, daemon=True)
            self.background_thread.start()
            
            logger.info("Advanced master controller started")

    def stop(self):
        """Stop the master controller."""
        with self.lock:
            if not self.is_running:
                return
                
            self.is_running = False
            
            if self.background_thread:
                self.background_thread.join(timeout=5.0)
                
            self.executor.shutdown(wait=True)
            logger.info("Advanced master controller stopped")

    def submit_task(self, 
                   task_type: TaskType,
                   description: str,
                   input_data: Dict[str, Any],
                   priority: Priority = Priority.MEDIUM,
                   context: Dict[str, Any] = None,
                   deadline: Optional[float] = None,
                   dependencies: List[str] = None,
                   callback: Optional[Callable] = None) -> str:
        """Submit a task for execution.
        
        Args:
            task_type: Type of task to execute
            description: Description of the task
            input_data: Input data for the task
            priority: Task priority
            context: Additional context
            deadline: Optional deadline
            dependencies: Task dependencies
            callback: Optional callback function
            
        Returns:
            Task ID
        """
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            description=description,
            input_data=input_data,
            context=context or {},
            created_at=time.time(),
            deadline=deadline,
            dependencies=dependencies or [],
            callback=callback
        )
        
        with self.lock:
            self.task_queue.append(task)
            self.task_queue.sort(key=lambda t: (-t.priority.value, t.created_at))
            
        logger.info(f"Task submitted: {task_id} ({task_type.value})")
        return task_id

    def execute_with_iterative_analysis(self, 
                                      task: Task,
                                      enable_iterations: bool = True) -> IterativeAnalysisResult:
        """Execute a task with iterative analysis and improvement.
        
        Args:
            task: The task to execute
            enable_iterations: Whether to enable iterative improvement
            
        Returns:
            Iterative analysis result
        """
        start_time = time.time()
        iterations = []
        confidence_progression = []
        
        # Initial execution
        initial_result = self._execute_single_task(task)
        current_result = initial_result
        confidence_progression.append(initial_result.confidence_score)
        
        if not enable_iterations or initial_result.confidence_score >= self.confidence_threshold:
            return IterativeAnalysisResult(
                initial_result=initial_result.result,
                final_result=current_result.result,
                iterations=[],
                improvement_metrics={},
                confidence_progression=confidence_progression,
                total_time=time.time() - start_time,
                success=initial_result.success
            )
        
        # Iterative improvement
        for iteration in range(self.max_iterations):
            logger.info(f"Starting iteration {iteration + 1} for task {task.task_id}")
            
            # Analyze current result and identify improvements
            analysis_result = self._analyze_for_improvement(current_result, task)
            
            if not analysis_result.get('improvements_needed', True):
                logger.info(f"No improvements needed after iteration {iteration + 1}")
                break
                
            # Apply improvements
            improved_task = self._apply_improvements(task, analysis_result)
            improved_result = self._execute_single_task(improved_task)
            
            # Track iteration
            iteration_data = {
                "iteration": iteration + 1,
                "analysis": analysis_result,
                "previous_confidence": current_result.confidence_score,
                "new_confidence": improved_result.confidence_score,
                "improvement": improved_result.confidence_score - current_result.confidence_score
            }
            iterations.append(iteration_data)
            confidence_progression.append(improved_result.confidence_score)
            
            # Notify iteration callbacks
            for callback in self.iteration_callbacks:
                try:
                    callback(task.task_id, iteration + 1, iteration_data)
                except Exception as e:
                    logger.error(f"Error in iteration callback: {e}")
            
            # Check if improvement is significant
            improvement = improved_result.confidence_score - current_result.confidence_score
            if improvement < self.improvement_threshold:
                logger.info(f"Improvement below threshold ({improvement:.3f} < {self.improvement_threshold})")
                break
                
            current_result = improved_result
            
            # Check if we've reached satisfactory confidence
            if current_result.confidence_score >= self.confidence_threshold:
                logger.info(f"Reached confidence threshold ({current_result.confidence_score:.3f})")
                break
        
        # Calculate improvement metrics
        improvement_metrics = {
            "confidence_improvement": current_result.confidence_score - initial_result.confidence_score,
            "iterations_performed": len(iterations),
            "final_confidence": current_result.confidence_score,
            "improvement_rate": (current_result.confidence_score - initial_result.confidence_score) / max(len(iterations), 1)
        }
        
        return IterativeAnalysisResult(
            initial_result=initial_result.result,
            final_result=current_result.result,
            iterations=iterations,
            improvement_metrics=improvement_metrics,
            confidence_progression=confidence_progression,
            total_time=time.time() - start_time,
            success=current_result.success
        )

    def _execute_single_task(self, task: Task) -> TaskResult:
        """Execute a single task without iterations."""
        start_time = time.time()

        try:
            # Route task to appropriate subsystem
            if task.task_type == TaskType.CODE_ANALYSIS:
                result = self._execute_code_analysis(task)
            elif task.task_type == TaskType.CODE_GENERATION:
                result = self._execute_code_generation(task)
            elif task.task_type == TaskType.DEBUGGING:
                result = self._execute_debugging(task)
            elif task.task_type == TaskType.OPTIMIZATION:
                result = self._execute_optimization(task)
            elif task.task_type == TaskType.REFACTORING:
                result = self._execute_refactoring(task)
            elif task.task_type == TaskType.TESTING:
                result = self._execute_testing(task)
            elif task.task_type == TaskType.DOCUMENTATION:
                result = self._execute_documentation(task)
            elif task.task_type == TaskType.SEARCH:
                result = self._execute_search(task)
            elif task.task_type == TaskType.EXECUTION:
                result = self._execute_code_execution(task)
            elif task.task_type == TaskType.LEARNING:
                result = self._execute_learning(task)
            elif task.task_type == TaskType.CONVERSATION:
                result = self._execute_conversation(task)
            else:
                result = {"error": f"Unknown task type: {task.task_type}"}

            # Calculate confidence score
            confidence = self._calculate_task_confidence(result, task)

            # Generate recommendations
            recommendations = self._generate_recommendations(result, task)

            # Generate next steps
            next_steps = self._generate_next_steps(result, task)

            return TaskResult(
                task_id=task.task_id,
                success=not isinstance(result, dict) or "error" not in result,
                result=result,
                execution_time=time.time() - start_time,
                confidence_score=confidence,
                recommendations=recommendations,
                next_steps=next_steps,
                metadata={"task_type": task.task_type.value, "context": task.context}
            )

        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {e}", exc_info=True)
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result={"error": str(e)},
                execution_time=time.time() - start_time,
                confidence_score=0.0,
                recommendations=["Review task input and try again"],
                next_steps=["debug", "retry"],
                metadata={"error": str(e)}
            )

    def _analyze_for_improvement(self, result: TaskResult, task: Task) -> Dict[str, Any]:
        """Analyze a result to identify potential improvements."""
        analysis_prompt = f"""
        Analyze the following task result and identify potential improvements:

        Task: {task.description}
        Task Type: {task.task_type.value}
        Current Confidence: {result.confidence_score:.2f}
        Success: {result.success}
        Result: {json.dumps(result.result, indent=2) if isinstance(result.result, dict) else str(result.result)[:1000]}

        Please identify:
        1. Areas for improvement
        2. Specific optimization opportunities
        3. Alternative approaches to try
        4. Parameters to adjust
        5. Whether improvements are needed (true/false)

        Respond in JSON format with keys: improvements_needed, areas_for_improvement, optimizations, alternatives, parameter_adjustments
        """

        try:
            response = self.model_manager.generate(
                prompt=analysis_prompt,
                system_prompt="You are an expert code analysis and improvement specialist."
            )

            # Try to parse as JSON
            if response.strip().startswith('{'):
                return json.loads(response)
            else:
                # Fallback parsing
                return {
                    "improvements_needed": result.confidence_score < self.confidence_threshold,
                    "areas_for_improvement": ["General optimization needed"],
                    "optimizations": ["Improve algorithm efficiency"],
                    "alternatives": ["Try different approach"],
                    "parameter_adjustments": ["Adjust model parameters"]
                }

        except Exception as e:
            logger.error(f"Error analyzing for improvement: {e}")
            return {
                "improvements_needed": result.confidence_score < self.confidence_threshold,
                "areas_for_improvement": [f"Analysis error: {str(e)}"],
                "optimizations": [],
                "alternatives": [],
                "parameter_adjustments": []
            }

    def _apply_improvements(self, task: Task, analysis: Dict[str, Any]) -> Task:
        """Apply improvements to a task based on analysis."""
        improved_context = task.context.copy()
        improved_input = task.input_data.copy()

        # Apply parameter adjustments
        if "parameter_adjustments" in analysis:
            for adjustment in analysis["parameter_adjustments"]:
                if "temperature" in adjustment.lower():
                    improved_context["temperature"] = min(improved_context.get("temperature", 0.7) + 0.1, 1.0)
                elif "max_tokens" in adjustment.lower():
                    improved_context["max_tokens"] = min(improved_context.get("max_tokens", 4096) + 1024, 8192)

        # Apply optimization suggestions
        if "optimizations" in analysis:
            improved_context["optimizations"] = analysis["optimizations"]

        # Apply alternative approaches
        if "alternatives" in analysis:
            improved_context["alternatives"] = analysis["alternatives"]

        # Create improved task
        return Task(
            task_id=task.task_id + "_improved",
            task_type=task.task_type,
            priority=task.priority,
            description=task.description + " (improved)",
            input_data=improved_input,
            context=improved_context,
            created_at=time.time(),
            deadline=task.deadline,
            dependencies=task.dependencies,
            callback=task.callback
        )

    def _background_worker(self):
        """Background worker for processing tasks."""
        while self.is_running:
            try:
                # Get next task
                task = None
                with self.lock:
                    if self.task_queue:
                        # Check dependencies
                        for i, candidate_task in enumerate(self.task_queue):
                            if self._are_dependencies_satisfied(candidate_task):
                                task = self.task_queue.pop(i)
                                self.active_tasks[task.task_id] = task
                                break

                if task:
                    # Execute task with iterative analysis
                    future = self.executor.submit(self.execute_with_iterative_analysis, task)

                    # Process result when complete
                    def handle_result(fut, current_task=task):
                        try:
                            iterative_result = fut.result()

                            # Create final task result
                            final_result = TaskResult(
                                task_id=current_task.task_id,
                                success=iterative_result.success,
                                result=iterative_result.final_result,
                                execution_time=iterative_result.total_time,
                                confidence_score=iterative_result.confidence_progression[-1] if iterative_result.confidence_progression else 0.0,
                                recommendations=[],
                                next_steps=[],
                                metadata={"iterative_analysis": iterative_result},
                                iterations_performed=len(iterative_result.iterations),
                                improvement_achieved=iterative_result.improvement_metrics.get("confidence_improvement", 0.0)
                            )

                            # Store result
                            with self.lock:
                                self.completed_tasks[current_task.task_id] = final_result
                                self.task_history.append(final_result)
                                if current_task.task_id in self.active_tasks:
                                    del self.active_tasks[current_task.task_id]

                            # Update metrics
                            self._update_metrics(final_result)

                            # Call callbacks
                            if current_task.callback:
                                try:
                                    current_task.callback(final_result)
                                except Exception as e:
                                    logger.error(f"Error in task callback: {e}")

                            for callback in self.task_callbacks:
                                try:
                                    callback(final_result)
                                except Exception as e:
                                    logger.error(f"Error in task callback: {e}")

                        except Exception as e:
                            logger.error(f"Error handling task result: {e}", exc_info=True)

                    future.add_done_callback(handle_result)
                else:
                    # No tasks available, sleep briefly
                    time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error in background worker: {e}", exc_info=True)
                time.sleep(1.0)

    def _are_dependencies_satisfied(self, task: Task) -> bool:
        """Check if task dependencies are satisfied."""
        if not task.dependencies:
            return True

        with self.lock:
            for dep_id in task.dependencies:
                if dep_id not in self.completed_tasks:
                    return False
                if not self.completed_tasks[dep_id].success:
                    return False
        return True

    def _execute_code_analysis(self, task: Task) -> Dict[str, Any]:
        """Execute code analysis task."""
        code = task.input_data.get("code", "")
        language = task.input_data.get("language", "python")

        # Debug logging
        logger.info(f"Code analysis task - input_data: {task.input_data}")
        logger.info(f"Code analysis task - extracted code: '{code}'")

        # If no code is provided, try to extract from description
        if not code:
            # Try to extract code from the task description
            import re
            code_patterns = [
                r'```(?:python|py)?\s*(.*?)```',  # Code blocks
                r'`([^`]+)`',  # Inline code
                r':\s*([^:\n]+(?:\([^)]*\))?)',  # After colon
            ]

            for pattern in code_patterns:
                matches = re.findall(pattern, task.description, re.DOTALL | re.IGNORECASE)
                if matches:
                    code = matches[0].strip()
                    logger.info(f"Extracted code from description: '{code}'")
                    break

        # If still no code, return an error with helpful message
        if not code:
            return {
                "error": "No code provided for analysis. Please provide code in your request.",
                "suggestion": "Try: 'analyze this code: print(\"hello\")'",
                "success": False
            }

        # Use AI assistant for analysis
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="analyze",
            code=code,
            language=language,
            prompt=task.description,
            context=task.context,
            preferences={},
            constraints=[]
        )

        response = self.ai_assistant.process_request(request)
        return response.result if response.success else {"error": response.result.get("error", "Analysis failed")}

    def _execute_code_generation(self, task: Task) -> Dict[str, Any]:
        """Execute code generation task."""
        prompt = task.input_data.get("prompt", task.description)
        language = task.input_data.get("language", "python")

        # Use AI assistant for generation
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="generate",
            code="",
            language=language,
            prompt=prompt,
            context=task.context,
            preferences={},
            constraints=[]
        )

        response = self.ai_assistant.process_request(request)
        return response.result if response.success else {"error": response.result.get("error", "Generation failed")}

    def _execute_debugging(self, task: Task) -> Dict[str, Any]:
        """Execute debugging task."""
        code = task.input_data.get("code", "")
        error_info = task.input_data.get("error_info", "")

        # Use predictive debugger
        debug_result = self.debugger.debug_code(code, error_info, task.context)
        return debug_result

    def _execute_optimization(self, task: Task) -> Dict[str, Any]:
        """Execute optimization task."""
        code = task.input_data.get("code", "")
        optimization_type = task.input_data.get("optimization_type", "performance")

        # Use optimization engine
        optimization_result = self.optimization_engine.optimize_code(code, optimization_type, task.context)
        return optimization_result

    def _execute_refactoring(self, task: Task) -> Dict[str, Any]:
        """Execute refactoring task."""
        code = task.input_data.get("code", "")
        refactoring_type = task.input_data.get("refactoring_type", "general")

        # Use refactoring engine
        refactoring_result = self.refactoring_engine.refactor_code(code, refactoring_type, task.context)
        return refactoring_result

    def _execute_testing(self, task: Task) -> Dict[str, Any]:
        """Execute testing task."""
        code = task.input_data.get("code", "")
        test_type = task.input_data.get("test_type", "unit")

        # Use AI assistant for test generation
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="test",
            code=code,
            language=task.input_data.get("language", "python"),
            prompt=f"Generate {test_type} tests for the provided code",
            context=task.context,
            preferences={"test_type": test_type},
            constraints=[]
        )

        response = self.ai_assistant.process_request(request)
        return response.result if response.success else {"error": response.result.get("error", "Test generation failed")}

    def _execute_documentation(self, task: Task) -> Dict[str, Any]:
        """Execute documentation task."""
        code = task.input_data.get("code", "")
        doc_type = task.input_data.get("doc_type", "docstring")

        # Use AI assistant for documentation
        request = AssistantRequest(
            request_id=str(uuid.uuid4()),
            request_type="document",
            code=code,
            language=task.input_data.get("language", "python"),
            prompt=f"Generate {doc_type} documentation for the provided code",
            context=task.context,
            preferences={"doc_type": doc_type},
            constraints=[]
        )

        response = self.ai_assistant.process_request(request)
        return response.result if response.success else {"error": response.result.get("error", "Documentation generation failed")}

    def _execute_search(self, task: Task) -> Dict[str, Any]:
        """Execute search task."""
        query = task.input_data.get("query", task.description)
        search_type = task.input_data.get("search_type", "semantic")

        # Use semantic indexer for search
        if search_type == "semantic":
            results = self.semantic_indexer.search(query, task.context.get("max_results", 10))
        else:
            # Use RAG system for enhanced search
            results = self.rag_system.search(query, task.context.get("max_results", 10))

        return {"results": results, "query": query, "search_type": search_type}

    def _execute_code_execution(self, task: Task) -> Dict[str, Any]:
        """Execute code execution task."""
        code = task.input_data.get("code", "")
        language = task.input_data.get("language", "python")

        # Use language processor for execution
        execution_result = self.language_processor.execute_code(code, language, task.context)
        return execution_result

    def _execute_learning(self, task: Task) -> Dict[str, Any]:
        """Execute learning task."""
        learning_data = task.input_data.get("learning_data", {})
        learning_type = task.input_data.get("learning_type", "feedback")

        # Use learning system
        learning_result = self.learning_system.process_learning(learning_data, learning_type, task.context)
        return learning_result

    def _execute_conversation(self, task: Task) -> Dict[str, Any]:
        """Execute conversation task."""
        message = task.input_data.get("query", task.description)

        # Use model manager for general conversation
        try:
            response = self.model_manager.generate(
                prompt=message,
                system_prompt="You are a helpful AI assistant. Respond naturally and helpfully to the user's message."
            )
            return {
                "response": response,
                "message": message,
                "success": True
            }
        except Exception as e:
            return {
                "error": f"Conversation failed: {str(e)}",
                "message": message,
                "success": False
            }

    def _calculate_task_confidence(self, result: Any, task: Task) -> float:
        """Calculate confidence score for a task result."""
        base_confidence = 0.5

        # Boost confidence based on result quality
        if isinstance(result, dict):
            if "error" in result:
                return 0.1
            if "success" in result and result["success"]:
                base_confidence += 0.3
            if "confidence" in result:
                return float(result["confidence"])

        # Boost confidence based on task complexity
        complexity_boost = {
            TaskType.CODE_ANALYSIS: 0.2,
            TaskType.CODE_GENERATION: 0.1,
            TaskType.DEBUGGING: 0.3,
            TaskType.OPTIMIZATION: 0.2,
            TaskType.REFACTORING: 0.2,
            TaskType.TESTING: 0.1,
            TaskType.DOCUMENTATION: 0.3,
            TaskType.SEARCH: 0.4,
            TaskType.EXECUTION: 0.2,
            TaskType.LEARNING: 0.1,
            TaskType.CONVERSATION: 0.4
        }.get(task.task_type, 0.1)

        return min(base_confidence + complexity_boost, 1.0)

    def _generate_recommendations(self, result: Any, task: Task) -> List[str]:
        """Generate recommendations based on task result."""
        recommendations = []

        if isinstance(result, dict) and "error" in result:
            recommendations.append("Review input data and try again")
            recommendations.append("Check for syntax or logical errors")
        else:
            recommendations.append("Consider optimizing the solution")
            recommendations.append("Add comprehensive testing")
            recommendations.append("Document the implementation")

        return recommendations

    def _generate_next_steps(self, result: Any, task: Task) -> List[str]:
        """Generate next steps based on task result."""
        next_steps = []

        if isinstance(result, dict) and "error" in result:
            next_steps.append("debug")
            next_steps.append("retry")
        else:
            next_steps.append("test")
            next_steps.append("optimize")
            next_steps.append("document")

        return next_steps

    def _update_metrics(self, result: TaskResult):
        """Update performance metrics."""
        with self.lock:
            self.metrics["total_tasks"] += 1
            if result.success:
                self.metrics["successful_tasks"] += 1

            # Update averages
            total = self.metrics["total_tasks"]
            self.metrics["average_execution_time"] = (
                (self.metrics["average_execution_time"] * (total - 1) + result.execution_time) / total
            )
            self.metrics["average_confidence"] = (
                (self.metrics["average_confidence"] * (total - 1) + result.confidence_score) / total
            )
            self.metrics["average_iterations"] = (
                (self.metrics["average_iterations"] * (total - 1) + result.iterations_performed) / total
            )
            self.metrics["improvement_rate"] = (
                (self.metrics["improvement_rate"] * (total - 1) + result.improvement_achieved) / total
            )

    def _on_analysis_complete(self, analysis_result):
        """Callback for when analysis is complete."""
        logger.info(f"Analysis complete: {analysis_result.analysis_id} - Success: {analysis_result.success}")

    def _on_decision_made(self, decision):
        """Callback for when a decision is made."""
        logger.info(f"Decision made: {decision.decision_type.value} - Confidence: {decision.confidence:.2f}")

    def _on_performance_update(self, metrics):
        """Callback for performance updates."""
        self.metrics["system_load"] = metrics.get("cpu_usage", 0.0)

    def _get_task_feedback(self) -> Dict[str, Any]:
        """Get feedback data for learning system."""
        with self.lock:
            return {
                "total_tasks": self.metrics["total_tasks"],
                "success_rate": self.metrics["successful_tasks"] / max(self.metrics["total_tasks"], 1),
                "average_confidence": self.metrics["average_confidence"],
                "recent_tasks": [asdict(task) for task in self.task_history[-10:]]
            }

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the master controller."""
        with self.lock:
            return {
                "is_running": self.is_running,
                "active_tasks": len(self.active_tasks),
                "queued_tasks": len(self.task_queue),
                "completed_tasks": len(self.completed_tasks),
                "metrics": self.metrics.copy(),
                "subsystems_status": {
                    "intelligence": bool(self.intelligence),
                    "ai_assistant": bool(self.ai_assistant),
                    "rag_system": bool(self.rag_system),
                    "context_completion": bool(self.context_completion),
                    "refactoring_engine": bool(self.refactoring_engine),
                    "debugger": bool(self.debugger),
                    "performance_analyzer": bool(self.performance_analyzer),
                    "optimization_engine": bool(self.optimization_engine),
                    "language_processor": bool(self.language_processor),
                    "semantic_indexer": bool(self.semantic_indexer),
                    "learning_system": bool(self.learning_system)
                }
            }

    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """Get result of a completed task."""
        with self.lock:
            return self.completed_tasks.get(task_id)

    def cancel_task(self, task_id: str) -> bool:
        """Cancel a queued task."""
        with self.lock:
            for i, task in enumerate(self.task_queue):
                if task.task_id == task_id:
                    self.task_queue.pop(i)
                    logger.info(f"Task cancelled: {task_id}")
                    return True
            return False

    def add_task_callback(self, callback: Callable[[TaskResult], None]):
        """Add a task completion callback."""
        self.task_callbacks.append(callback)

    def add_progress_callback(self, callback: Callable[[str, float], None]):
        """Add a progress callback."""
        self.progress_callbacks.append(callback)

    def add_iteration_callback(self, callback: Callable[[str, int, Dict[str, Any]], None]):
        """Add an iteration callback."""
        self.iteration_callbacks.append(callback)

    def process_natural_language_request(self, request: str, context: Dict[str, Any] = None) -> str:
        """Process a natural language request and execute appropriate tasks.

        Args:
            request: Natural language request
            context: Additional context

        Returns:
            Task ID of the submitted task
        """
        # Use AI to interpret the request
        interpretation_prompt = f"""
        Interpret the following natural language request and determine the appropriate task type and parameters:

        Request: {request}
        Context: {json.dumps(context or {}, indent=2)}

        Available task types: {[t.value for t in TaskType]}

        Respond in JSON format with keys: task_type, description, input_data, priority, estimated_complexity
        """

        try:
            response = self.model_manager.generate(
                prompt=interpretation_prompt,
                system_prompt="You are an expert at interpreting natural language requests for coding tasks."
            )

            # Parse response
            if response.strip().startswith('{'):
                interpretation = json.loads(response)
            else:
                # Fallback interpretation - detect if it's likely a conversation or code task
                is_code_related = any(keyword in request.lower() for keyword in [
                    'code', 'function', 'class', 'variable', 'debug', 'error', 'analyze',
                    'generate', 'write', 'create', 'fix', 'optimize', 'refactor', 'test'
                ])

                fallback_task_type = "code_analysis" if is_code_related else "conversation"

                # Try to extract code from the request if it's code-related
                input_data = {"query": request}
                if is_code_related:
                    # Simple code extraction - look for code patterns
                    import re
                    code_patterns = [
                        r'```(?:python|py)?\s*(.*?)```',  # Code blocks
                        r'`([^`]+)`',  # Inline code
                        r':\s*([^:\n]+(?:\([^)]*\))?)',  # After colon (like "analyze this code: print('hello')")
                    ]

                    extracted_code = ""
                    for pattern in code_patterns:
                        matches = re.findall(pattern, request, re.DOTALL | re.IGNORECASE)
                        if matches:
                            extracted_code = matches[0].strip()
                            break

                    if extracted_code:
                        input_data["code"] = extracted_code

                interpretation = {
                    "task_type": fallback_task_type,
                    "description": request,
                    "input_data": input_data,
                    "priority": "medium",
                    "estimated_complexity": "medium"
                }

            # Map task type
            task_type_map = {t.value: t for t in TaskType}
            task_type = task_type_map.get(interpretation.get("task_type", "code_analysis"), TaskType.CODE_ANALYSIS)

            # Map priority
            priority_map = {"low": Priority.LOW, "medium": Priority.MEDIUM, "high": Priority.HIGH, "critical": Priority.CRITICAL}
            priority = priority_map.get(interpretation.get("priority", "medium"), Priority.MEDIUM)

            # Submit task
            return self.submit_task(
                task_type=task_type,
                description=interpretation.get("description", request),
                input_data=interpretation.get("input_data", {"query": request}),
                priority=priority,
                context=context or {}
            )

        except Exception as e:
            logger.error(f"Error processing natural language request: {e}")
            # Fallback - detect if it's likely a conversation or code task
            is_code_related = any(keyword in request.lower() for keyword in [
                'code', 'function', 'class', 'variable', 'debug', 'error', 'analyze',
                'generate', 'write', 'create', 'fix', 'optimize', 'refactor', 'test'
            ])

            fallback_task_type = TaskType.CODE_ANALYSIS if is_code_related else TaskType.CONVERSATION

            # Try to extract code from the request if it's code-related
            input_data = {"query": request}
            if is_code_related:
                # Simple code extraction - look for code patterns
                import re
                code_patterns = [
                    r'```(?:python|py)?\s*(.*?)```',  # Code blocks
                    r'`([^`]+)`',  # Inline code
                    r':\s*([^:\n]+(?:\([^)]*\))?)',  # After colon (like "analyze this code: print('hello')")
                ]

                extracted_code = ""
                for pattern in code_patterns:
                    matches = re.findall(pattern, request, re.DOTALL | re.IGNORECASE)
                    if matches:
                        extracted_code = matches[0].strip()
                        break

                if extracted_code:
                    input_data["code"] = extracted_code

            return self.submit_task(
                task_type=fallback_task_type,
                description=request,
                input_data=input_data,
                priority=Priority.MEDIUM,
                context=context or {}
            )
